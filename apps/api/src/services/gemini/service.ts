import { GeminiClient } from "./client";
import {
  CleanedDescriptionSchema,
  CleanedDescriptionJSONSchema,
  type CleanedDescriptionOutput,
  TextExtractSchema,
  type TextExtractOutput,
  TextExtractJSONSchema,
} from "./schemas";
import * as v from "valibot";
import djb2a from "@mx/shared/utils/djb2a";
import { getLogger } from "@logtape/logtape";

import { encodeBase64 } from "@std/encoding/base64";

import descCleanerPrompt from "./prompts/desc-clean.txt";
import textExtractPrompt from "./prompts/text-extract.txt";
import { errorToJSON } from "../../lib/logging";
import type { GeminiImageBlob } from "./validation";

const logger = getLogger(["mx-api", "gemini", "service"]);

/**
 * Business logic service for Gemini AI operations
 */
export class GeminiService {
  private client: GeminiClient;

  constructor(apiKey: string) {
    this.client = new GeminiClient(apiKey);
    logger.debug("GeminiService initialized");
  }

  /**
   * Clean YouTube video description using text input
   */
  async cleanDescription(
    description: string,
  ): Promise<CleanedDescriptionOutput> {
    if (!description || description.trim().length === 0) {
      throw new Error("Description cannot be empty");
    }

    logger.debug("Cleaning description", {
      originalLength: description.length,
      hash: djb2a(description),
    });

    const startTime = Date.now();

    try {
      const p1 = await this.client
        .generate([{ text: description }], {
          model: "gemini-2.5-flash-lite-preview-06-17",
          responseMimeType: "application/json",
          responseSchema: CleanedDescriptionJSONSchema,
          systemInstruction: [descCleanerPrompt],
        })
        .then((res) => v.safeParse(CleanedDescriptionSchema, res.text));

      if (p1.success) {
        const duration = Date.now() - startTime;

        logger.info("Description cleaning completed", {
          duration,
          originalLength: description.length,
          cleanedLength: p1.output.cleanedText.length,
          hash: djb2a(description),
        });

        return p1.output;
      }
      logger.error("Description cleaning failed for flash lite", {
        issues: p1.issues,
      });
      const p2 = await this.client
        .generate([{ text: description }], {
          model: "gemini-2.5-flash",
          responseMimeType: "application/json",
          responseSchema: CleanedDescriptionJSONSchema,
          systemInstruction: [descCleanerPrompt],
        })
        .then((res) => v.parse(CleanedDescriptionSchema, res.text));

      const duration = Date.now() - startTime;
      logger.info("Description cleaning completed", {
        duration,
        originalLength: description.length,
        cleanedLength: p2.cleanedText.length,
        hash: djb2a(description),
      });
      return p2;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error("Description cleaning failed", {
        duration,
        originalLength: description.length,
        error: errorToJSON(error),
        hash: djb2a(description),
      });
      throw error;
    }
  }

  async extractText(
    image: GeminiImageBlob,
  ): Promise<TextExtractOutput & { usageMetadata?: any }> {
    logger.debug("Extracting text", {
      size: image.size,
      type: image.type,
    });

    const startTime = Date.now();

    try {
      const response = await this.client.generate(
        [
          {
            inlineData: {
              mimeType: image.type,
              data: encodeBase64(await image.arrayBuffer()),
            },
          },
        ],
        {
          model: "gemini-2.5-flash",
          responseMimeType: "application/json",
          responseSchema: TextExtractJSONSchema,
          systemInstruction: [textExtractPrompt],
        },
      );

      const result = v.parse(TextExtractSchema, response.text);

      logger.info("Text extraction completed", {
        duration: Date.now() - startTime,
        metadata: result.metadata,
        status: result.status,
        usageMetadata: response.usageMetadata,
      });

      return {
        ...result,
        usageMetadata: response.usageMetadata,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error("Text extraction failed", {
        duration,
        error: errorToJSON(error),
      });
      throw error;
    }
  }

  /**
   * Generate cache key for content
   */
  generateCacheKey(content: string): string {
    return djb2a(content).toString(36);
  }
}
