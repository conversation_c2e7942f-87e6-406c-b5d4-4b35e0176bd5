import { Hono } from "hono";
import type { HonoEnv } from "../types/env";
import { GeminiService } from "../services/gemini/service";
import { createMiddleware } from "hono/factory";
import { getLogger } from "@logtape/logtape";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";

import { errorToJSON } from "../lib/logging";
import { vValidator } from "@hono/valibot-validator";
import { TextExtractFormDataSchema } from "../services/gemini/validation";
import {
  BillingService,
  InsufficientCreditsError,
} from "@mx/db/services/billing";
import { CostCalculator } from "@mx/db/services/cost-calculator";
import * as schema from "@mx/db/schema";

const logger = getLogger(["mx-api", "text-extract"]);

// Extend the environment type to include our services
type ServiceVariables = {
  services: {
    gemini: GeminiService;
    billing: BillingService;
    costCalculator: CostCalculator;
  };
};

type ExtendedHonoEnv = HonoEnv & {
  Variables: ServiceVariables;
};

// Create a middleware to initialize services
const servicesMiddleware = createMiddleware<ExtendedHonoEnv>(
  async (c, next) => {
    // Initialize database connection
    const client = postgres(c.env.POSTGRES_DIRECT_URL);
    const db = drizzle(client, { schema });

    // Initialize services
    const geminiService = new GeminiService(c.env.GOOGLE_API_KEY);
    const billingService = new BillingService(db);
    const costCalculator = new CostCalculator();

    c.set("services", {
      gemini: geminiService,
      billing: billingService,
      costCalculator: costCalculator,
    });

    await next();
  },
);

const app = new Hono<ExtendedHonoEnv>()
  .use(servicesMiddleware)
  .post("/", vValidator("form", TextExtractFormDataSchema), async (c) => {
    logger.info("Processing text extraction request");

    const { services } = c.var;
    const { image } = c.req.valid("form");
    const user = c.get("user");

    if (!user) {
      return c.json({ error: "Authentication required" }, 401);
    }

    let reservationId: string | null = null;

    try {
      logger.debug("Processing validated image file", {
        size: image.size,
        type: image.type,
        userId: user.id,
      });

      // Step 1: Estimate cost and reserve credits
      const estimatedCost = services.costCalculator.estimateTextExtractCost(
        image.size,
      );

      logger.info("Reserving credits for text extraction", {
        userId: user.id,
        estimatedCost,
        imageSize: image.size,
      });

      const reservation = await services.billing.reserveCredits(
        user.id,
        "text-extract",
        estimatedCost,
        { imageSize: image.size, imageType: image.type },
      );

      reservationId = reservation.id;

      logger.debug("Credits reserved successfully", {
        reservationId,
        reservedAmount: reservation.reservedAmount,
      });

      // Step 2: Call Gemini service to extract text
      const startTime = Date.now();
      const result = await services.gemini.extractText(image);
      const duration = Date.now() - startTime;

      // Step 3: Calculate actual cost and settle reservation
      const actualCost = services.costCalculator.calculateTextExtractCost(
        result.usageMetadata || {},
        image.size,
      );

      const settlement = await services.billing.settleReservation(
        reservationId,
        actualCost,
        result.usageMetadata,
      );

      logger.info("Text extraction completed successfully", {
        duration,
        status: result.status,
        contentLength: result.content.length,
        metadata: result.metadata,
        billing: {
          estimatedCost,
          actualCost: settlement.actualCost,
          refundAmount: settlement.refundAmount,
          transactionId: settlement.transactionId,
        },
      });

      // Return the result with billing information
      return c.json({
        ...result,
        billing: {
          cost: settlement.actualCost,
          refunded: settlement.refundAmount,
        },
      });
    } catch (error) {
      // Release reservation if something went wrong
      if (reservationId) {
        try {
          await services.billing.releaseReservation(reservationId, "cancelled");
          logger.info("Released reservation due to error", { reservationId });
        } catch (releaseError) {
          logger.error("Failed to release reservation", {
            reservationId,
            error: errorToJSON(releaseError),
          });
        }
      }

      // Handle insufficient credits error
      if (error instanceof InsufficientCreditsError) {
        logger.warn("Text extraction failed due to insufficient credits", {
          userId: user.id,
          error: error.message,
        });

        return c.json(
          {
            error: "Insufficient credits",
            message: error.message,
            code: "INSUFFICIENT_CREDITS",
          },
          402, // Payment Required
        );
      }

      // Handle other errors
      logger.error("Text extraction failed", {
        error: errorToJSON(error),
        userId: user.id,
        reservationId,
      });

      return c.json(
        {
          status: "error",
          content: "Text extraction failed",
          metadata: {
            has_tables: false,
            has_lists: false,
            text_regions_count: 0,
          },
        },
        500,
      );
    }
  });

export default app;
