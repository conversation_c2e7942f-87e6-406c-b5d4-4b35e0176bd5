---
mx-uid: nttvxbfgryxc8va2fi327dk8
video: "[[big-buck-bunny-1080p-30sec.mp4]]"
cover: "[[mx-img-nttvxbfgryxc8va2fi327dk8-pt27_40s.png]]"
---

# <PERSON> <PERSON>

This is a test note for screenshot.

- ![[mx-img-nttvxbfgryxc8va2fi327dk8-pt19_80s.jpg|Big Buck Bunny - 00:19|50]] [[big-buck-bunny-1080p-30sec.mp4#t=00:19.80|00:19]] 

> I want to have database schema in my TypeScript codebase, I don’t wanna deal with SQL migration files.  
> I want <PERSON><PERSON><PERSON> to “push” my schema directly to the database

That’s a **codebase first** approach. You have your TypeScript Drizzle schema as a **source of truth** and Drizzle let’s you push schema changes to the database using [`drizzle-kit push`](https://orm.drizzle.team/docs/drizzle-kit-push) command.

That’s the best approach for rapid prototyping and we’ve seen dozens of teams and solo developers successfully using it as a primary migrations flow in their production applications.

src/schema.ts

```
import * as p from "drizzle-orm/pg-core";export const users = p.pgTable("users", {  id: p.serial().primaryKey(),  name: p.text(),  email: p.text().unique(), // <--- added column};
```

```
Add column to `users` table                                                                          
┌──────────────────────────┐                  
│ + email: text().unique() │                  
└─┬────────────────────────┘                  
  │                                           
  v                                           
┌──────────────────────────┐                  
│ ~ drizzle-kit push       │                  
└─┬────────────────────────┘                  
  │                                           ┌──────────────────────────┐
  └ Pull current datatabase schema ---------> │                          │
                                              │                          │
  ┌ Generate alternations based on diff <---- │         DATABASE         │
  │                                           │                          │
  └ Apply migrations to the database -------> │                          │
                                       │      └──────────────────────────┘
                                       │
  ┌────────────────────────────────────┴──────────────┐
   ALTER TABLE `users` ADD COLUMN `email` TEXT UNIQUE; 

```