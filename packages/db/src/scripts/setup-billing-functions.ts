#!/usr/bin/env node

import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import {
  cleanupExpiredReservationsFunction,
  getBillingStatsFunction,
  initializeUserCreditsFunction,
} from "../schema/functions.js";

/**
 * <PERSON>ript to setup billing functions and cron jobs in Supabase
 * Run this after pushing the schema to create the functions and schedule cleanup tasks
 */

async function setupBillingFunctions() {
  const connectionString = process.env.POSTGRES_DIRECT_URL;
  
  if (!connectionString) {
    console.error("POSTGRES_DIRECT_URL environment variable is required");
    process.exit(1);
  }

  const client = postgres(connectionString);
  const db = drizzle(client);

  try {
    console.log("🔧 Setting up billing functions...");

    // Create the cleanup function
    console.log("📝 Creating cleanup_expired_reservations function...");
    await db.execute(cleanupExpiredReservationsFunction);

    // Create the billing stats function
    console.log("📊 Creating get_billing_stats function...");
    await db.execute(getBillingStatsFunction);

    // Create the user initialization function
    console.log("👤 Creating initialize_user_credits function...");
    await db.execute(initializeUserCreditsFunction);

    // Enable pg_cron extension
    console.log("⏰ Enabling pg_cron extension...");
    await db.execute(sql`CREATE EXTENSION IF NOT EXISTS pg_cron;`);

    // Schedule the cleanup job (every 5 minutes)
    console.log("📅 Scheduling cleanup cron job...");
    await db.execute(sql`
      SELECT cron.schedule(
        'cleanup-expired-reservations',
        '*/5 * * * *',
        'SELECT cleanup_expired_reservations();'
      );
    `);

    // Create a monitoring view for cron jobs
    console.log("👁️ Creating cron job monitoring view...");
    await db.execute(sql`
      CREATE OR REPLACE VIEW billing_cron_status AS
      SELECT 
        jobname,
        schedule,
        active,
        last_run,
        next_run,
        CASE 
          WHEN last_run IS NULL THEN 'Never run'
          WHEN last_run < NOW() - INTERVAL '10 minutes' THEN 'Overdue'
          ELSE 'Running normally'
        END as status
      FROM cron.job
      WHERE jobname LIKE '%billing%' OR jobname LIKE '%cleanup%';
    `);

    console.log("✅ Billing functions and cron jobs setup completed!");
    console.log("\n📋 Summary:");
    console.log("- cleanup_expired_reservations() function created");
    console.log("- get_billing_stats() function created");
    console.log("- initialize_user_credits() function created");
    console.log("- Cron job scheduled to run every 5 minutes");
    console.log("- Monitoring view 'billing_cron_status' created");
    
    console.log("\n🔍 To monitor the cron job:");
    console.log("SELECT * FROM billing_cron_status;");
    
    console.log("\n📊 To get billing statistics:");
    console.log("SELECT * FROM get_billing_stats();");

  } catch (error) {
    console.error("❌ Error setting up billing functions:", error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run the setup if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  setupBillingFunctions().catch(console.error);
}

export { setupBillingFunctions };
