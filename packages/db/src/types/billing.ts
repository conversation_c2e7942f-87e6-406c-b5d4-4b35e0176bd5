/**
 * Type definitions for the billing system
 */

import type { SelectCreditReservation } from "../schema/index.js";
import type { ReservationStatus } from "./billing.js";
import type { SelectCreditTransaction } from "./billing.js";
import type { UserBalance } from "./billing.js";

// Re-export types from schema
export type {
  InsertUserCredit,
  SelectUserCredit,
  InsertCreditReservation,
  SelectCreditReservation,
  InsertCreditTransaction,
  SelectCreditTransaction,
} from "../schema/index.js";

// Re-export service types
export type {
  ServiceType,
  ReservationStatus,
  TransactionType,
  ReservationResult,
  SettlementResult,
  UserBalance,
} from "../services/billing.js";

// Re-export cost calculator types
export type {
  GenerateContentResponseUsageMetadata,
  ModalityTokenCount,
  MediaModality,
  TrafficType,
  ServiceRates,
} from "../services/cost-calculator.js";

// Re-export error classes
export {
  InsufficientCreditsError,
  ReservationNotFoundError,
  ReservationAlreadySettledError,
} from "../services/billing.js";

// Additional utility types for API responses
export interface BillingInfo {
  cost: string;
  refunded: string;
}

export interface BillingError {
  error: string;
  message: string;
  code: string;
}

export interface CostBreakdown {
  baseCost: number;
  imageSizeCost: number;
  inputTokenCost: number;
  outputTokenCost: number;
  cachedTokenCost: number;
  totalCost: number;
}

export interface BillingStats {
  totalUsers: number;
  totalCreditsIssued: string;
  totalCreditsSpent: string;
  totalTransactions: number;
  activeReservations: number;
  expiredReservations: number;
}

export interface CronJobStatus {
  jobname: string;
  schedule: string;
  active: boolean;
  lastRun: Date | null;
  nextRun: Date | null;
  status: "Never run" | "Overdue" | "Running normally";
}

export interface BillingOverview {
  userId: string;
  availableCredits: string;
  reservedCredits: string;
  totalCredits: string;
  activeReservationsCount: number;
  recentTransactionsCount: number;
  creditsCreatedAt: Date;
  creditsUpdatedAt: Date;
}

// Constants
export const BILLING_CONSTANTS = {
  DEFAULT_RESERVATION_TIMEOUT_MINUTES: 10,
  MINIMUM_CREDIT_AMOUNT: 0.01,
  MAXIMUM_CREDIT_AMOUNT: 1000.0,
  DEFAULT_INITIAL_CREDITS: 10.0,
} as const;

// Error codes
export const BILLING_ERROR_CODES = {
  INSUFFICIENT_CREDITS: "INSUFFICIENT_CREDITS",
  RESERVATION_NOT_FOUND: "RESERVATION_NOT_FOUND",
  RESERVATION_ALREADY_SETTLED: "RESERVATION_ALREADY_SETTLED",
  INVALID_AMOUNT: "INVALID_AMOUNT",
  USER_NOT_FOUND: "USER_NOT_FOUND",
  BILLING_SERVICE_ERROR: "BILLING_SERVICE_ERROR",
} as const;

export type BillingErrorCode =
  (typeof BILLING_ERROR_CODES)[keyof typeof BILLING_ERROR_CODES];

// Service configuration types
export interface BillingServiceConfig {
  defaultReservationTimeoutMinutes?: number;
  defaultInitialCredits?: number;
  enableAutoCleanup?: boolean;
  cleanupIntervalMinutes?: number;
}

export interface CostCalculatorConfig {
  textExtractRates?: {
    baseImageCost?: number;
    inputTokenRate?: number;
    outputTokenRate?: number;
    imageSizeMultiplier?: number;
  };
  descriptionCleanRates?: {
    inputTokenRate?: number;
    outputTokenRate?: number;
  };
}

// Database function result types
export interface CleanupResult {
  cleanedCount: number;
  totalRefunded: string;
}

export interface InitializationResult {
  success: boolean;
  message?: string;
}

// Validation types
export interface CostValidationResult {
  isValid: boolean;
  warnings: string[];
}

// API request/response types for billing endpoints
export interface AddCreditsRequest {
  userId: string;
  amount: number;
  description?: string;
}

export interface GetBalanceResponse {
  balance: UserBalance | null;
}

export interface GetTransactionsRequest {
  userId: string;
  limit?: number;
  offset?: number;
  startDate?: string;
  endDate?: string;
}

export interface GetTransactionsResponse {
  transactions: SelectCreditTransaction[];
  total: number;
  hasMore: boolean;
}

export interface GetReservationsRequest {
  userId: string;
  status?: ReservationStatus;
  limit?: number;
  offset?: number;
}

export interface GetReservationsResponse {
  reservations: SelectCreditReservation[];
  total: number;
  hasMore: boolean;
}

// Admin API types
export interface AdminBillingStatsResponse {
  stats: BillingStats;
  cronStatus: CronJobStatus[];
  recentActivity: {
    newUsers: number;
    totalTransactions: number;
    totalCreditsSpent: string;
  };
}

export interface AdminUserBillingResponse {
  user: {
    id: string;
    balance: UserBalance;
    recentTransactions: SelectCreditTransaction[];
    activeReservations: SelectCreditReservation[];
  };
}

// Event types for potential future event system
export interface BillingEvent {
  type:
    | "credit_reserved"
    | "credit_charged"
    | "credit_refunded"
    | "credit_added";
  userId: string;
  amount: string;
  metadata?: any;
  timestamp: Date;
}

// Webhook types for potential integrations
export interface BillingWebhookPayload {
  event: BillingEvent;
  signature: string;
  timestamp: number;
}

// Audit log types
export interface BillingAuditLog {
  id: string;
  userId: string;
  action: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}
