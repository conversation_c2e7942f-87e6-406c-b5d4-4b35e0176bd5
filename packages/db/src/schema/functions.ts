import { sql } from "drizzle-orm";

/**
 * SQL function to cleanup expired credit reservations
 * This function will be executed by Supabase pg_cron
 */
export const cleanupExpiredReservationsFunction = sql`
CREATE OR REPLACE FUNCTION cleanup_expired_reservations()
RETURNS TABLE(
  cleaned_count INTEGER,
  total_refunded DECIMAL(10,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  cleaned_count INTEGER := 0;
  total_refunded DECIMAL(10,2) := 0;
  reservation_record RECORD;
BEGIN
  -- Process expired reservations one by one to ensure data consistency
  FOR reservation_record IN
    SELECT id, user_id, reserved_amount
    FROM credit_reservations
    WHERE status = 'active' 
    AND expires_at < NOW()
    FOR UPDATE
  LOOP
    -- Update user credits (return reserved amount to available)
    UPDATE user_credits 
    SET 
      available_credits = available_credits + reservation_record.reserved_amount,
      reserved_credits = reserved_credits - reservation_record.reserved_amount,
      updated_at = NOW()
    WHERE user_id = reservation_record.user_id;
    
    -- Mark reservation as expired
    UPDATE credit_reservations
    SET 
      status = 'expired', 
      updated_at = NOW()
    WHERE id = reservation_record.id;
    
    -- Create refund transaction record
    INSERT INTO credit_transactions (
      user_id, 
      reservation_id,
      transaction_type, 
      amount, 
      service_type,
      description
    ) VALUES (
      reservation_record.user_id,
      reservation_record.id,
      'refund',
      -reservation_record.reserved_amount, -- negative amount for refund
      'system',
      'Expired reservation cleanup - automatic refund'
    );
    
    -- Update counters
    cleaned_count := cleaned_count + 1;
    total_refunded := total_refunded + reservation_record.reserved_amount;
  END LOOP;
  
  -- Log cleanup operation
  IF cleaned_count > 0 THEN
    RAISE NOTICE 'Cleaned up % expired reservations, refunded % credits total', 
      cleaned_count, total_refunded;
  END IF;
  
  -- Return results
  RETURN QUERY SELECT cleaned_count, total_refunded;
END;
$$;
`;

/**
 * SQL function to get billing statistics
 * Useful for monitoring and reporting
 */
export const getBillingStatsFunction = sql`
CREATE OR REPLACE FUNCTION get_billing_stats(
  start_date TIMESTAMP DEFAULT NOW() - INTERVAL '30 days',
  end_date TIMESTAMP DEFAULT NOW()
)
RETURNS TABLE(
  total_users INTEGER,
  total_credits_issued DECIMAL(10,2),
  total_credits_spent DECIMAL(10,2),
  total_transactions INTEGER,
  active_reservations INTEGER,
  expired_reservations INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(DISTINCT user_id) FROM user_credits)::INTEGER as total_users,
    (SELECT COALESCE(SUM(available_credits + reserved_credits), 0) FROM user_credits) as total_credits_issued,
    (SELECT COALESCE(SUM(amount), 0) FROM credit_transactions 
     WHERE transaction_type = 'charge' 
     AND created_at BETWEEN start_date AND end_date) as total_credits_spent,
    (SELECT COUNT(*) FROM credit_transactions 
     WHERE created_at BETWEEN start_date AND end_date)::INTEGER as total_transactions,
    (SELECT COUNT(*) FROM credit_reservations WHERE status = 'active')::INTEGER as active_reservations,
    (SELECT COUNT(*) FROM credit_reservations WHERE status = 'expired')::INTEGER as expired_reservations;
END;
$$;
`;

/**
 * SQL function to initialize user credits
 * Called when a new user signs up
 */
export const initializeUserCreditsFunction = sql`
CREATE OR REPLACE FUNCTION initialize_user_credits(
  p_user_id UUID,
  p_initial_credits DECIMAL(10,2) DEFAULT 10.00
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Insert user credits record if not exists
  INSERT INTO user_credits (user_id, available_credits, reserved_credits)
  VALUES (p_user_id, p_initial_credits, 0.00)
  ON CONFLICT (user_id) DO NOTHING;
  
  -- Create initial credit transaction record
  INSERT INTO credit_transactions (
    user_id,
    transaction_type,
    amount,
    service_type,
    description
  ) VALUES (
    p_user_id,
    'topup',
    p_initial_credits,
    'system',
    'Initial credit allocation for new user'
  );
  
  RAISE NOTICE 'Initialized credits for user %: % credits', p_user_id, p_initial_credits;
END;
$$;
`;
