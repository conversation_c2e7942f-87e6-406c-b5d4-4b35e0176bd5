import { sql } from "drizzle-orm";
import {
  uuid,
  pgTable,
  timestamp,
  decimal,
  text,
  json,
  pgPolicy,
  index,
  check,
} from "drizzle-orm/pg-core";
import {
  authUsers,
  authUid,
  authenticatedRole,
  serviceRole,
} from "drizzle-orm/supabase";

// User credits table - tracks available and reserved credits
export const userCredits = pgTable(
  "user_credits",
  {
    userId: uuid("user_id")
      .primaryKey()
      .references(() => authUsers.id, { onDelete: "cascade" }),
    availableCredits: decimal("available_credits", { precision: 10, scale: 2 })
      .notNull()
      .default("0.00"),
    reservedCredits: decimal("reserved_credits", { precision: 10, scale: 2 })
      .notNull()
      .default("0.00"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at")
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    // RLS policies
    pgPolicy("users can view own credits", {
      for: "select",
      to: authenticatedRole,
      using: sql`${authUid} = ${table.userId}`,
    }),
    pgPolicy("service role can manage all credits", {
      for: "all",
      to: serviceRole,
      using: sql`true`,
    }),
    // Constraints
    check(
      "available_credits_non_negative",
      sql`${table.availableCredits} >= 0`,
    ),
    check("reserved_credits_non_negative", sql`${table.reservedCredits} >= 0`),
    // Indexes
    index("user_credits_user_id_idx").on(table.userId),
  ],
);

// Credit reservations table - tracks pre-authorized credits
export const creditReservations = pgTable(
  "credit_reservations",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => authUsers.id, { onDelete: "cascade" }),
    serviceType: text("service_type").notNull(),
    reservedAmount: decimal("reserved_amount", {
      precision: 10,
      scale: 2,
    }).notNull(),
    status: text("status").notNull().default("active"),
    requestMetadata: json("request_metadata"),
    expiresAt: timestamp("expires_at").notNull(),
    createdAt: timestamp("created_at").notNull().defaultNow(),
    updatedAt: timestamp("updated_at")
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    // RLS policies
    pgPolicy("users can view own reservations", {
      for: "select",
      to: authenticatedRole,
      using: sql`${authUid} = ${table.userId}`,
    }),
    pgPolicy("service role can manage all reservations", {
      for: "all",
      to: serviceRole,
      using: sql`true`,
    }),
    // Constraints
    check(
      "status_valid",
      sql`${table.status} IN ('active', 'settled', 'expired', 'cancelled')`,
    ),
    check("reserved_amount_positive", sql`${table.reservedAmount} > 0`),
    check("expires_at_future", sql`${table.expiresAt} > ${table.createdAt}`),
    // Indexes
    index("credit_reservations_user_id_idx").on(table.userId),
    index("credit_reservations_status_expires_idx").on(
      table.status,
      table.expiresAt,
    ),
    index("credit_reservations_user_status_idx").on(table.userId, table.status),
  ],
);

// Credit transactions table - final billing records
export const creditTransactions = pgTable(
  "credit_transactions",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => authUsers.id, { onDelete: "cascade" }),
    reservationId: uuid("reservation_id").references(
      () => creditReservations.id,
    ),
    transactionType: text("transaction_type").notNull(),
    amount: decimal("amount", { precision: 10, scale: 2 }).notNull(),
    serviceType: text("service_type"),
    serviceMetadata: json("service_metadata"),
    description: text("description"),
    createdAt: timestamp("created_at").notNull().defaultNow(),
  },
  (table) => [
    // RLS policies
    pgPolicy("users can view own transactions", {
      for: "select",
      to: authenticatedRole,
      using: sql`${authUid} = ${table.userId}`,
    }),
    pgPolicy("service role can manage all transactions", {
      for: "all",
      to: serviceRole,
      using: sql`true`,
    }),
    // Constraints
    check(
      "transaction_type_valid",
      sql`${table.transactionType} IN ('charge', 'refund', 'topup')`,
    ),
    check("amount_not_zero", sql`${table.amount} != 0`),
    // Indexes
    index("credit_transactions_user_id_idx").on(table.userId),
    index("credit_transactions_user_created_idx").on(
      table.userId,
      table.createdAt,
    ),
    index("credit_transactions_reservation_idx").on(table.reservationId),
  ],
);

// Import and execute SQL functions
import {
  cleanupExpiredReservationsFunction,
  getBillingStatsFunction,
  initializeUserCreditsFunction,
} from "./functions.js";

// Execute functions to create them in the database
export const billingFunctions = [
  cleanupExpiredReservationsFunction,
  getBillingStatsFunction,
  initializeUserCreditsFunction,
];

// Type exports
export type InsertUserCredit = typeof userCredits.$inferInsert;
export type SelectUserCredit = typeof userCredits.$inferSelect;
export type InsertCreditReservation = typeof creditReservations.$inferInsert;
export type SelectCreditReservation = typeof creditReservations.$inferSelect;
export type InsertCreditTransaction = typeof creditTransactions.$inferInsert;
export type SelectCreditTransaction = typeof creditTransactions.$inferSelect;
