/**
 * Cost calculation service for different AI services
 * Converts various usage metrics to unified credit costs
 */

// Gemini API usage metadata types (from the provided structure)
export interface GenerateContentResponseUsageMetadata {
  cacheTokensDetails?: ModalityTokenCount[];
  cachedContentTokenCount?: number;
  candidatesTokenCount?: number;
  candidatesTokensDetails?: ModalityTokenCount[];
  promptTokenCount?: number;
  promptTokensDetails?: ModalityTokenCount[];
  thoughtsTokenCount?: number;
  toolUsePromptTokenCount?: number;
  toolUsePromptTokensDetails?: ModalityTokenCount[];
  totalTokenCount?: number;
  trafficType?: TrafficType;
}

export interface ModalityTokenCount {
  modality?: MediaModality;
  tokenCount?: number;
}

export enum MediaModality {
  MODALITY_UNSPECIFIED = "MODALITY_UNSPECIFIED",
  TEXT = "TEXT",
  IMAGE = "IMAGE",
  VIDEO = "VIDEO",
  AUDIO = "AUDIO",
  DOCUMENT = "DOCUMENT"
}

export enum TrafficType {
  TRAFFIC_TYPE_UNSPECIFIED = "TRAFFIC_TYPE_UNSPECIFIED",
  ON_DEMAND = "ON_DEMAND",
  PROVISIONED_THROUGHPUT = "PROVISIONED_THROUGHPUT"
}

// Service-specific cost configuration
export interface ServiceRates {
  textExtract: {
    baseImageCost: number;        // Base cost per image processed
    inputTokenRate: number;       // Cost per 1000 input tokens
    outputTokenRate: number;      // Cost per 1000 output tokens
    imageSizeMultiplier: number;  // Multiplier based on image size
  };
  descriptionClean: {
    inputTokenRate: number;       // Cost per 1000 input tokens
    outputTokenRate: number;      // Cost per 1000 output tokens
  };
}

// Default rate configuration
const DEFAULT_RATES: ServiceRates = {
  textExtract: {
    baseImageCost: 0.5,           // 0.5 credits per image
    inputTokenRate: 0.001,        // 0.001 credits per 1000 input tokens
    outputTokenRate: 0.002,       // 0.002 credits per 1000 output tokens
    imageSizeMultiplier: 0.000001, // Additional cost per byte
  },
  descriptionClean: {
    inputTokenRate: 0.0005,       // 0.0005 credits per 1000 input tokens
    outputTokenRate: 0.001,       // 0.001 credits per 1000 output tokens
  },
};

export class CostCalculator {
  private rates: ServiceRates;

  constructor(customRates?: Partial<ServiceRates>) {
    this.rates = {
      textExtract: { ...DEFAULT_RATES.textExtract, ...customRates?.textExtract },
      descriptionClean: { ...DEFAULT_RATES.descriptionClean, ...customRates?.descriptionClean },
    };
  }

  /**
   * Estimate cost for text extraction before API call
   * Based on image size and expected token usage
   */
  estimateTextExtractCost(imageSize: number): number {
    const rates = this.rates.textExtract;
    
    // Base cost for image processing
    let estimatedCost = rates.baseImageCost;
    
    // Add cost based on image size (larger images = more tokens)
    estimatedCost += imageSize * rates.imageSizeMultiplier;
    
    // Add estimated token costs (rough estimate)
    // Assume ~100 input tokens for image + ~500 output tokens for extracted text
    const estimatedInputTokens = 100;
    const estimatedOutputTokens = 500;
    
    estimatedCost += (estimatedInputTokens / 1000) * rates.inputTokenRate;
    estimatedCost += (estimatedOutputTokens / 1000) * rates.outputTokenRate;
    
    // Round up to ensure we reserve enough
    return Math.ceil(estimatedCost * 100) / 100; // Round to 2 decimal places, round up
  }

  /**
   * Calculate actual cost for text extraction based on Gemini usage metadata
   */
  calculateTextExtractCost(
    usageMetadata: GenerateContentResponseUsageMetadata,
    imageSize?: number
  ): number {
    const rates = this.rates.textExtract;
    let actualCost = rates.baseImageCost;

    // Add image size cost if provided
    if (imageSize) {
      actualCost += imageSize * rates.imageSizeMultiplier;
    }

    // Calculate token costs from usage metadata
    const inputTokens = usageMetadata.promptTokenCount || 0;
    const outputTokens = usageMetadata.candidatesTokenCount || 0;

    actualCost += (inputTokens / 1000) * rates.inputTokenRate;
    actualCost += (outputTokens / 1000) * rates.outputTokenRate;

    // Handle cached content tokens (usually cheaper)
    const cachedTokens = usageMetadata.cachedContentTokenCount || 0;
    if (cachedTokens > 0) {
      // Cached tokens are typically 50% cheaper
      actualCost += (cachedTokens / 1000) * rates.inputTokenRate * 0.5;
    }

    return Math.round(actualCost * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Estimate cost for description cleaning before API call
   */
  estimateDescriptionCleanCost(descriptionLength: number): number {
    const rates = this.rates.descriptionClean;
    
    // Rough estimate: 1 token per 4 characters for input
    const estimatedInputTokens = Math.ceil(descriptionLength / 4);
    // Assume output is ~70% of input length
    const estimatedOutputTokens = Math.ceil(estimatedInputTokens * 0.7);
    
    let estimatedCost = 0;
    estimatedCost += (estimatedInputTokens / 1000) * rates.inputTokenRate;
    estimatedCost += (estimatedOutputTokens / 1000) * rates.outputTokenRate;
    
    // Minimum cost and round up
    return Math.max(0.01, Math.ceil(estimatedCost * 100) / 100);
  }

  /**
   * Calculate actual cost for description cleaning based on Gemini usage metadata
   */
  calculateDescriptionCleanCost(
    usageMetadata: GenerateContentResponseUsageMetadata
  ): number {
    const rates = this.rates.descriptionClean;
    
    const inputTokens = usageMetadata.promptTokenCount || 0;
    const outputTokens = usageMetadata.candidatesTokenCount || 0;
    
    let actualCost = 0;
    actualCost += (inputTokens / 1000) * rates.inputTokenRate;
    actualCost += (outputTokens / 1000) * rates.outputTokenRate;
    
    // Handle cached content
    const cachedTokens = usageMetadata.cachedContentTokenCount || 0;
    if (cachedTokens > 0) {
      actualCost += (cachedTokens / 1000) * rates.inputTokenRate * 0.5;
    }
    
    return Math.round(actualCost * 100) / 100;
  }

  /**
   * Get current service rates configuration
   */
  getServiceRates(): ServiceRates {
    return { ...this.rates };
  }

  /**
   * Update service rates (admin operation)
   */
  updateServiceRates(newRates: Partial<ServiceRates>): void {
    if (newRates.textExtract) {
      this.rates.textExtract = { ...this.rates.textExtract, ...newRates.textExtract };
    }
    if (newRates.descriptionClean) {
      this.rates.descriptionClean = { ...this.rates.descriptionClean, ...newRates.descriptionClean };
    }
  }

  /**
   * Calculate cost breakdown for debugging/transparency
   */
  getTextExtractCostBreakdown(
    usageMetadata: GenerateContentResponseUsageMetadata,
    imageSize?: number
  ): {
    baseCost: number;
    imageSizeCost: number;
    inputTokenCost: number;
    outputTokenCost: number;
    cachedTokenCost: number;
    totalCost: number;
  } {
    const rates = this.rates.textExtract;
    
    const baseCost = rates.baseImageCost;
    const imageSizeCost = imageSize ? imageSize * rates.imageSizeMultiplier : 0;
    
    const inputTokens = usageMetadata.promptTokenCount || 0;
    const outputTokens = usageMetadata.candidatesTokenCount || 0;
    const cachedTokens = usageMetadata.cachedContentTokenCount || 0;
    
    const inputTokenCost = (inputTokens / 1000) * rates.inputTokenRate;
    const outputTokenCost = (outputTokens / 1000) * rates.outputTokenRate;
    const cachedTokenCost = (cachedTokens / 1000) * rates.inputTokenRate * 0.5;
    
    const totalCost = baseCost + imageSizeCost + inputTokenCost + outputTokenCost + cachedTokenCost;
    
    return {
      baseCost: Math.round(baseCost * 100) / 100,
      imageSizeCost: Math.round(imageSizeCost * 100) / 100,
      inputTokenCost: Math.round(inputTokenCost * 100) / 100,
      outputTokenCost: Math.round(outputTokenCost * 100) / 100,
      cachedTokenCost: Math.round(cachedTokenCost * 100) / 100,
      totalCost: Math.round(totalCost * 100) / 100,
    };
  }

  /**
   * Validate that a cost calculation is reasonable
   * Helps prevent billing errors from unexpected usage patterns
   */
  validateCost(cost: number, serviceType: 'textExtract' | 'descriptionClean'): {
    isValid: boolean;
    warnings: string[];
  } {
    const warnings: string[] = [];
    let isValid = true;

    // Basic validation
    if (cost < 0) {
      warnings.push('Cost cannot be negative');
      isValid = false;
    }

    if (cost > 100) {
      warnings.push('Cost exceeds maximum expected value (100 credits)');
      isValid = false;
    }

    // Service-specific validation
    if (serviceType === 'textExtract') {
      if (cost < 0.1) {
        warnings.push('Text extraction cost seems unusually low');
      }
      if (cost > 10) {
        warnings.push('Text extraction cost seems unusually high');
      }
    } else if (serviceType === 'descriptionClean') {
      if (cost < 0.001) {
        warnings.push('Description cleaning cost seems unusually low');
      }
      if (cost > 1) {
        warnings.push('Description cleaning cost seems unusually high');
      }
    }

    return { isValid, warnings };
  }
}
