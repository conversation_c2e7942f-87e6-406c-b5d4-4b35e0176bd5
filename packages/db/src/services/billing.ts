import type { PostgresJsDatabase } from "drizzle-orm/postgres-js";
import { eq, sql } from "drizzle-orm";
import {
  userCredits,
  creditReservations,
  creditTransactions,
} from "../schema/index.js";

// Types for billing operations
export type ServiceType = "text-extract" | "desc-clean" | "system";
export type ReservationStatus = "active" | "settled" | "expired" | "cancelled";
export type TransactionType = "charge" | "refund" | "topup";

export interface ReservationResult {
  id: string;
  userId: string;
  reservedAmount: string;
  expiresAt: Date;
}

export interface SettlementResult {
  actualCost: string;
  refundAmount: string;
  transactionId: string;
}

export interface UserBalance {
  userId: string;
  availableCredits: string;
  reservedCredits: string;
  totalCredits: string;
}

// Custom error classes
export class InsufficientCreditsError extends Error {
  constructor(available: string, required: string) {
    super(`Insufficient credits: available ${available}, required ${required}`);
    this.name = "InsufficientCreditsError";
  }
}

export class ReservationNotFoundError extends Error {
  constructor(reservationId: string) {
    super(`Reservation not found: ${reservationId}`);
    this.name = "ReservationNotFoundError";
  }
}

export class ReservationAlreadySettledError extends Error {
  constructor(reservationId: string, status: string) {
    super(
      `Reservation ${reservationId} already settled with status: ${status}`,
    );
    this.name = "ReservationAlreadySettledError";
  }
}

/**
 * BillingService handles all credit-related operations
 * Implements the pre-authorization + settlement billing model
 */
export class BillingService {
  private db: PostgresJsDatabase<typeof import("../schema/index.js")>;

  constructor(
    database: PostgresJsDatabase<typeof import("../schema/index.js")>,
  ) {
    this.db = database;
  }

  /**
   * Reserve credits for a service call (pre-authorization)
   * This locks the specified amount from user's available credits
   */
  async reserveCredits(
    userId: string,
    serviceType: ServiceType,
    estimatedCost: number,
    requestMetadata?: any,
    timeoutMinutes = 10,
  ): Promise<ReservationResult> {
    const expiresAt = new Date(Date.now() + timeoutMinutes * 60 * 1000);
    const estimatedCostStr = estimatedCost.toFixed(2);

    return await this.db.transaction(async (tx) => {
      // Lock user credits row for update
      const userCredit = await tx
        .select()
        .from(userCredits)
        .where(eq(userCredits.userId, userId))
        .for("update")
        .then((rows) => rows[0]);

      if (!userCredit) {
        // Initialize user credits if not exists
        await tx.insert(userCredits).values({
          userId,
          availableCredits: "0.00",
          reservedCredits: "0.00",
        });
        throw new InsufficientCreditsError("0.00", estimatedCostStr);
      }

      // Check if user has sufficient available credits
      const availableCredits = Number.parseFloat(userCredit.availableCredits);
      if (availableCredits < estimatedCost) {
        throw new InsufficientCreditsError(
          userCredit.availableCredits,
          estimatedCostStr,
        );
      }

      // Create reservation record
      const [reservation] = await tx
        .insert(creditReservations)
        .values({
          userId,
          serviceType,
          reservedAmount: estimatedCostStr,
          status: "active",
          requestMetadata,
          expiresAt,
        })
        .returning({
          id: creditReservations.id,
          userId: creditReservations.userId,
          reservedAmount: creditReservations.reservedAmount,
          expiresAt: creditReservations.expiresAt,
        });

      if (!reservation) {
        throw new Error("Failed to create reservation");
      }

      // Update user credits (move from available to reserved)
      await tx
        .update(userCredits)
        .set({
          availableCredits: sql`${userCredits.availableCredits} - ${estimatedCostStr}`,
          reservedCredits: sql`${userCredits.reservedCredits} + ${estimatedCostStr}`,
        })
        .where(eq(userCredits.userId, userId));

      return {
        id: reservation.id,
        userId: reservation.userId,
        reservedAmount: reservation.reservedAmount,
        expiresAt: reservation.expiresAt,
      };
    });
  }

  /**
   * Settle a reservation with actual cost (final billing)
   * This charges the actual amount and refunds any excess
   */
  async settleReservation(
    reservationId: string,
    actualCost: number,
    serviceMetadata?: any,
  ): Promise<SettlementResult> {
    const actualCostStr = actualCost.toFixed(2);

    return await this.db.transaction(async (tx) => {
      // Get and lock reservation
      const reservation = await tx
        .select()
        .from(creditReservations)
        .where(eq(creditReservations.id, reservationId))
        .for("update")
        .then((rows) => rows[0]);

      if (!reservation) {
        throw new ReservationNotFoundError(reservationId);
      }

      if (reservation.status !== "active") {
        throw new ReservationAlreadySettledError(
          reservationId,
          reservation.status,
        );
      }

      const reservedAmount = Number.parseFloat(reservation.reservedAmount);
      const refundAmount = Math.max(0, reservedAmount - actualCost);
      const refundAmountStr = refundAmount.toFixed(2);

      // Update user credits
      await tx
        .update(userCredits)
        .set({
          reservedCredits: sql`${userCredits.reservedCredits} - ${reservation.reservedAmount}`,
          availableCredits: sql`${userCredits.availableCredits} + ${refundAmountStr}`,
        })
        .where(eq(userCredits.userId, reservation.userId));

      // Mark reservation as settled
      await tx
        .update(creditReservations)
        .set({ status: "settled" })
        .where(eq(creditReservations.id, reservationId));

      // Create charge transaction
      const [chargeTransaction] = await tx
        .insert(creditTransactions)
        .values({
          userId: reservation.userId,
          reservationId,
          transactionType: "charge",
          amount: actualCostStr,
          serviceType: reservation.serviceType,
          serviceMetadata,
          description: `${reservation.serviceType} service charge`,
        })
        .returning({
          id: creditTransactions.id,
        });

      if (!chargeTransaction) {
        throw new Error("Failed to create charge transaction");
      }

      // Create refund transaction if there's a refund
      if (refundAmount > 0) {
        await tx.insert(creditTransactions).values({
          userId: reservation.userId,
          reservationId,
          transactionType: "refund",
          amount: `-${refundAmountStr}`,
          serviceType: reservation.serviceType,
          description: "Unused reservation refund",
        });
      }

      return {
        actualCost: actualCostStr,
        refundAmount: refundAmountStr,
        transactionId: chargeTransaction.id,
      };
    });
  }

  /**
   * Release a reservation without charging (for failed operations)
   * This returns all reserved credits back to available
   */
  async releaseReservation(
    reservationId: string,
    reason: "cancelled" | "expired" = "cancelled",
  ): Promise<void> {
    await this.db.transaction(async (tx) => {
      // Get and lock reservation
      const reservation = await tx
        .select()
        .from(creditReservations)
        .where(eq(creditReservations.id, reservationId))
        .for("update")
        .then((rows) => rows[0]);

      if (!reservation) {
        throw new ReservationNotFoundError(reservationId);
      }

      if (reservation.status !== "active") {
        // Already processed, nothing to do
        return;
      }

      // Return reserved credits to available
      await tx
        .update(userCredits)
        .set({
          reservedCredits: sql`${userCredits.reservedCredits} - ${reservation.reservedAmount}`,
          availableCredits: sql`${userCredits.availableCredits} + ${reservation.reservedAmount}`,
        })
        .where(eq(userCredits.userId, reservation.userId));

      // Mark reservation as cancelled/expired
      await tx
        .update(creditReservations)
        .set({ status: reason })
        .where(eq(creditReservations.id, reservationId));

      // Create refund transaction
      await tx.insert(creditTransactions).values({
        userId: reservation.userId,
        reservationId,
        transactionType: "refund",
        amount: `-${reservation.reservedAmount}`,
        serviceType: reservation.serviceType,
        description: `Reservation ${reason} - full refund`,
      });
    });
  }

  /**
   * Get user's current credit balance
   */
  async getUserBalance(userId: string): Promise<UserBalance | null> {
    const userCredit = await this.db
      .select()
      .from(userCredits)
      .where(eq(userCredits.userId, userId))
      .then((rows) => rows[0]);

    if (!userCredit) {
      return null;
    }

    const availableCredits = Number.parseFloat(userCredit.availableCredits);
    const reservedCredits = Number.parseFloat(userCredit.reservedCredits);

    return {
      userId: userCredit.userId,
      availableCredits: userCredit.availableCredits,
      reservedCredits: userCredit.reservedCredits,
      totalCredits: (availableCredits + reservedCredits).toFixed(2),
    };
  }

  /**
   * Add credits to user account (admin operation)
   */
  async addCredits(
    userId: string,
    amount: number,
    description = "Credit top-up",
  ): Promise<void> {
    const amountStr = amount.toFixed(2);

    await this.db.transaction(async (tx) => {
      // Upsert user credits
      await tx
        .insert(userCredits)
        .values({
          userId,
          availableCredits: amountStr,
          reservedCredits: "0.00",
        })
        .onConflictDoUpdate({
          target: userCredits.userId,
          set: {
            availableCredits: sql`${userCredits.availableCredits} + ${amountStr}`,
          },
        });

      // Create transaction record
      await tx.insert(creditTransactions).values({
        userId,
        transactionType: "topup",
        amount: amountStr,
        serviceType: "system",
        description,
      });
    });
  }
}
