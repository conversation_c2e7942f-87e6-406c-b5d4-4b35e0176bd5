import { describe, it, expect, beforeEach } from "vitest";
import { 
  CostCalculator, 
  type GenerateContentResponseUsageMetadata,
  MediaModality,
  TrafficType 
} from "../cost-calculator.js";

describe("CostCalculator", () => {
  let calculator: CostCalculator;

  beforeEach(() => {
    calculator = new CostCalculator();
  });

  describe("estimateTextExtractCost", () => {
    it("should estimate cost for small image", () => {
      const imageSize = 50000; // 50KB
      const cost = calculator.estimateTextExtractCost(imageSize);
      
      expect(cost).toBeGreaterThan(0);
      expect(cost).toBeLessThan(2); // Should be reasonable for small image
    });

    it("should estimate higher cost for larger image", () => {
      const smallImageSize = 50000; // 50KB
      const largeImageSize = 500000; // 500KB
      
      const smallCost = calculator.estimateTextExtractCost(smallImageSize);
      const largeCost = calculator.estimateTextExtractCost(largeImageSize);
      
      expect(largeCost).toBeGreaterThan(smallCost);
    });

    it("should round up to ensure sufficient reservation", () => {
      const imageSize = 1000;
      const cost = calculator.estimateTextExtractCost(imageSize);
      
      // Should be rounded to 2 decimal places
      expect(cost.toString()).toMatch(/^\d+\.\d{2}$/);
    });
  });

  describe("calculateTextExtractCost", () => {
    it("should calculate cost based on token usage", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1000,
        candidatesTokenCount: 500,
        totalTokenCount: 1500,
      };

      const cost = calculator.calculateTextExtractCost(usageMetadata, 100000);
      
      expect(cost).toBeGreaterThan(0);
      expect(cost).toBeLessThan(10); // Should be reasonable
    });

    it("should handle cached content tokens with discount", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1000,
        candidatesTokenCount: 500,
        cachedContentTokenCount: 200,
        totalTokenCount: 1700,
      };

      const costWithCache = calculator.calculateTextExtractCost(usageMetadata, 100000);
      
      const usageWithoutCache: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1200, // 1000 + 200 (no cache discount)
        candidatesTokenCount: 500,
        totalTokenCount: 1700,
      };

      const costWithoutCache = calculator.calculateTextExtractCost(usageWithoutCache, 100000);
      
      // Cost with cache should be lower due to 50% discount on cached tokens
      expect(costWithCache).toBeLessThan(costWithoutCache);
    });

    it("should handle missing usage metadata gracefully", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {};
      
      const cost = calculator.calculateTextExtractCost(usageMetadata, 100000);
      
      // Should still have base image cost
      expect(cost).toBeGreaterThan(0);
    });
  });

  describe("estimateDescriptionCleanCost", () => {
    it("should estimate cost based on description length", () => {
      const shortDescription = "Short description";
      const longDescription = "This is a much longer description that contains many more words and characters, which should result in a higher estimated cost for processing.";
      
      const shortCost = calculator.estimateDescriptionCleanCost(shortDescription.length);
      const longCost = calculator.estimateDescriptionCleanCost(longDescription.length);
      
      expect(longCost).toBeGreaterThan(shortCost);
    });

    it("should have minimum cost", () => {
      const cost = calculator.estimateDescriptionCleanCost(1);
      expect(cost).toBeGreaterThanOrEqual(0.01);
    });
  });

  describe("calculateDescriptionCleanCost", () => {
    it("should calculate cost based on input/output tokens", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 500,
        candidatesTokenCount: 300,
        totalTokenCount: 800,
      };

      const cost = calculator.calculateDescriptionCleanCost(usageMetadata);
      
      expect(cost).toBeGreaterThan(0);
      expect(cost).toBeLessThan(1); // Should be reasonable for description cleaning
    });
  });

  describe("getServiceRates", () => {
    it("should return current service rates", () => {
      const rates = calculator.getServiceRates();
      
      expect(rates.textExtract).toBeDefined();
      expect(rates.textExtract.baseImageCost).toBeGreaterThan(0);
      expect(rates.textExtract.inputTokenRate).toBeGreaterThan(0);
      expect(rates.textExtract.outputTokenRate).toBeGreaterThan(0);
      
      expect(rates.descriptionClean).toBeDefined();
      expect(rates.descriptionClean.inputTokenRate).toBeGreaterThan(0);
      expect(rates.descriptionClean.outputTokenRate).toBeGreaterThan(0);
    });
  });

  describe("updateServiceRates", () => {
    it("should update text extract rates", () => {
      const newRates = {
        textExtract: {
          baseImageCost: 1.0,
          inputTokenRate: 0.002,
        }
      };

      calculator.updateServiceRates(newRates);
      const rates = calculator.getServiceRates();
      
      expect(rates.textExtract.baseImageCost).toBe(1.0);
      expect(rates.textExtract.inputTokenRate).toBe(0.002);
      // Other rates should remain unchanged
      expect(rates.textExtract.outputTokenRate).toBe(0.002); // default value
    });

    it("should update description clean rates", () => {
      const newRates = {
        descriptionClean: {
          inputTokenRate: 0.001,
          outputTokenRate: 0.002,
        }
      };

      calculator.updateServiceRates(newRates);
      const rates = calculator.getServiceRates();
      
      expect(rates.descriptionClean.inputTokenRate).toBe(0.001);
      expect(rates.descriptionClean.outputTokenRate).toBe(0.002);
    });
  });

  describe("getTextExtractCostBreakdown", () => {
    it("should provide detailed cost breakdown", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1000,
        candidatesTokenCount: 500,
        cachedContentTokenCount: 200,
        totalTokenCount: 1700,
      };

      const breakdown = calculator.getTextExtractCostBreakdown(usageMetadata, 100000);
      
      expect(breakdown.baseCost).toBeGreaterThan(0);
      expect(breakdown.imageSizeCost).toBeGreaterThan(0);
      expect(breakdown.inputTokenCost).toBeGreaterThan(0);
      expect(breakdown.outputTokenCost).toBeGreaterThan(0);
      expect(breakdown.cachedTokenCost).toBeGreaterThan(0);
      expect(breakdown.totalCost).toBe(
        breakdown.baseCost + 
        breakdown.imageSizeCost + 
        breakdown.inputTokenCost + 
        breakdown.outputTokenCost + 
        breakdown.cachedTokenCost
      );
    });

    it("should handle missing image size", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1000,
        candidatesTokenCount: 500,
      };

      const breakdown = calculator.getTextExtractCostBreakdown(usageMetadata);
      
      expect(breakdown.imageSizeCost).toBe(0);
      expect(breakdown.totalCost).toBeGreaterThan(0);
    });
  });

  describe("validateCost", () => {
    it("should validate reasonable text extract costs", () => {
      const result = calculator.validateCost(2.5, 'textExtract');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(0);
    });

    it("should warn about unusually low text extract costs", () => {
      const result = calculator.validateCost(0.05, 'textExtract');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Text extraction cost seems unusually low');
    });

    it("should warn about unusually high text extract costs", () => {
      const result = calculator.validateCost(15.0, 'textExtract');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Text extraction cost seems unusually high');
    });

    it("should reject negative costs", () => {
      const result = calculator.validateCost(-1.0, 'textExtract');
      
      expect(result.isValid).toBe(false);
      expect(result.warnings).toContain('Cost cannot be negative');
    });

    it("should reject extremely high costs", () => {
      const result = calculator.validateCost(150.0, 'textExtract');
      
      expect(result.isValid).toBe(false);
      expect(result.warnings).toContain('Cost exceeds maximum expected value (100 credits)');
    });

    it("should validate description clean costs", () => {
      const result = calculator.validateCost(0.1, 'descriptionClean');
      
      expect(result.isValid).toBe(true);
      expect(result.warnings).toHaveLength(0);
    });
  });

  describe("custom rates", () => {
    it("should use custom rates when provided", () => {
      const customCalculator = new CostCalculator({
        textExtract: {
          baseImageCost: 2.0,
          inputTokenRate: 0.005,
        }
      });

      const rates = customCalculator.getServiceRates();
      expect(rates.textExtract.baseImageCost).toBe(2.0);
      expect(rates.textExtract.inputTokenRate).toBe(0.005);
      // Should use default for unspecified rates
      expect(rates.textExtract.outputTokenRate).toBe(0.002);
    });
  });

  describe("precision and rounding", () => {
    it("should maintain proper decimal precision", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1,
        candidatesTokenCount: 1,
      };

      const cost = calculator.calculateTextExtractCost(usageMetadata, 1);
      
      // Should be rounded to 2 decimal places
      expect(cost.toString()).toMatch(/^\d+\.\d{2}$/);
    });

    it("should handle very small token counts", () => {
      const usageMetadata: GenerateContentResponseUsageMetadata = {
        promptTokenCount: 1,
        candidatesTokenCount: 1,
      };

      const cost = calculator.calculateTextExtractCost(usageMetadata);
      
      expect(cost).toBeGreaterThan(0);
      expect(cost).toBeLessThan(1);
    });
  });
});
