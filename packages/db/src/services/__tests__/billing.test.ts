import { describe, it, expect, beforeEach, afterEach } from "vitest";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { BillingService, InsufficientCreditsError, ReservationNotFoundError } from "../billing.js";
import { userCredits, creditReservations, creditTransactions } from "../../schema/index.js";
import { eq } from "drizzle-orm";

// Test database setup
const TEST_DATABASE_URL = process.env.TEST_DATABASE_URL || "postgresql://test:test@localhost:5432/test_billing";

describe("BillingService", () => {
  let client: postgres.Sql;
  let db: ReturnType<typeof drizzle>;
  let billingService: BillingService;
  
  const testUserId = "test-user-123";
  const testUserId2 = "test-user-456";

  beforeEach(async () => {
    client = postgres(TEST_DATABASE_URL);
    db = drizzle(client, { schema: { userCredits, creditReservations, creditTransactions } });
    billingService = new BillingService(db);

    // Clean up test data
    await db.delete(creditTransactions);
    await db.delete(creditReservations);
    await db.delete(userCredits);
  });

  afterEach(async () => {
    await client.end();
  });

  describe("reserveCredits", () => {
    it("should successfully reserve credits for a user with sufficient balance", async () => {
      // Setup: Add credits to user
      await billingService.addCredits(testUserId, 10.0, "Test setup");

      // Test: Reserve credits
      const reservation = await billingService.reserveCredits(
        testUserId,
        "text-extract",
        5.0,
        { test: true }
      );

      expect(reservation.userId).toBe(testUserId);
      expect(reservation.reservedAmount).toBe("5.00");
      expect(reservation.id).toBeDefined();

      // Verify user balance
      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("5.00");
      expect(balance?.reservedCredits).toBe("5.00");
    });

    it("should throw InsufficientCreditsError when user has insufficient balance", async () => {
      // Setup: Add insufficient credits
      await billingService.addCredits(testUserId, 2.0, "Test setup");

      // Test: Try to reserve more than available
      await expect(
        billingService.reserveCredits(testUserId, "text-extract", 5.0)
      ).rejects.toThrow(InsufficientCreditsError);
    });

    it("should initialize user credits if user doesn't exist", async () => {
      // Test: Try to reserve credits for non-existent user
      await expect(
        billingService.reserveCredits(testUserId, "text-extract", 1.0)
      ).rejects.toThrow(InsufficientCreditsError);

      // Verify user was created with zero balance
      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("0.00");
      expect(balance?.reservedCredits).toBe("0.00");
    });
  });

  describe("settleReservation", () => {
    it("should settle reservation with exact cost", async () => {
      // Setup
      await billingService.addCredits(testUserId, 10.0, "Test setup");
      const reservation = await billingService.reserveCredits(testUserId, "text-extract", 5.0);

      // Test: Settle with exact cost
      const settlement = await billingService.settleReservation(
        reservation.id,
        5.0,
        { tokens: 1000 }
      );

      expect(settlement.actualCost).toBe("5.00");
      expect(settlement.refundAmount).toBe("0.00");

      // Verify final balance
      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("5.00");
      expect(balance?.reservedCredits).toBe("0.00");
    });

    it("should settle reservation with partial refund", async () => {
      // Setup
      await billingService.addCredits(testUserId, 10.0, "Test setup");
      const reservation = await billingService.reserveCredits(testUserId, "text-extract", 5.0);

      // Test: Settle with lower actual cost
      const settlement = await billingService.settleReservation(
        reservation.id,
        3.0,
        { tokens: 600 }
      );

      expect(settlement.actualCost).toBe("3.00");
      expect(settlement.refundAmount).toBe("2.00");

      // Verify final balance (5 available + 2 refunded = 7 available)
      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("7.00");
      expect(balance?.reservedCredits).toBe("0.00");
    });

    it("should throw error for non-existent reservation", async () => {
      await expect(
        billingService.settleReservation("non-existent-id", 1.0)
      ).rejects.toThrow(ReservationNotFoundError);
    });
  });

  describe("releaseReservation", () => {
    it("should release reservation and refund all credits", async () => {
      // Setup
      await billingService.addCredits(testUserId, 10.0, "Test setup");
      const reservation = await billingService.reserveCredits(testUserId, "text-extract", 5.0);

      // Test: Release reservation
      await billingService.releaseReservation(reservation.id, "cancelled");

      // Verify full refund
      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("10.00");
      expect(balance?.reservedCredits).toBe("0.00");

      // Verify reservation status
      const reservationRecord = await db
        .select()
        .from(creditReservations)
        .where(eq(creditReservations.id, reservation.id))
        .then(rows => rows[0]);
      
      expect(reservationRecord.status).toBe("cancelled");
    });

    it("should handle releasing already processed reservation gracefully", async () => {
      // Setup
      await billingService.addCredits(testUserId, 10.0, "Test setup");
      const reservation = await billingService.reserveCredits(testUserId, "text-extract", 5.0);
      
      // First settlement
      await billingService.settleReservation(reservation.id, 3.0);

      // Test: Try to release already settled reservation
      await expect(
        billingService.releaseReservation(reservation.id, "cancelled")
      ).resolves.not.toThrow();
    });
  });

  describe("getUserBalance", () => {
    it("should return null for non-existent user", async () => {
      const balance = await billingService.getUserBalance("non-existent-user");
      expect(balance).toBeNull();
    });

    it("should return correct balance for existing user", async () => {
      await billingService.addCredits(testUserId, 15.5, "Test setup");
      
      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.userId).toBe(testUserId);
      expect(balance?.availableCredits).toBe("15.50");
      expect(balance?.reservedCredits).toBe("0.00");
      expect(balance?.totalCredits).toBe("15.50");
    });
  });

  describe("addCredits", () => {
    it("should add credits to new user", async () => {
      await billingService.addCredits(testUserId, 20.0, "Initial credits");

      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("20.00");
      expect(balance?.reservedCredits).toBe("0.00");
    });

    it("should add credits to existing user", async () => {
      // Setup: Initial credits
      await billingService.addCredits(testUserId, 10.0, "Initial");
      
      // Test: Add more credits
      await billingService.addCredits(testUserId, 5.0, "Top-up");

      const balance = await billingService.getUserBalance(testUserId);
      expect(balance?.availableCredits).toBe("15.00");
    });

    it("should create transaction record for credit addition", async () => {
      await billingService.addCredits(testUserId, 25.0, "Test transaction");

      const transactions = await db
        .select()
        .from(creditTransactions)
        .where(eq(creditTransactions.userId, testUserId));

      expect(transactions).toHaveLength(1);
      expect(transactions[0].transactionType).toBe("topup");
      expect(transactions[0].amount).toBe("25.00");
      expect(transactions[0].description).toBe("Test transaction");
    });
  });

  describe("concurrent operations", () => {
    it("should handle concurrent reservations correctly", async () => {
      // Setup: Add credits
      await billingService.addCredits(testUserId, 10.0, "Test setup");

      // Test: Concurrent reservations
      const promises = [
        billingService.reserveCredits(testUserId, "text-extract", 3.0),
        billingService.reserveCredits(testUserId, "text-extract", 4.0),
        billingService.reserveCredits(testUserId, "text-extract", 5.0), // This should fail
      ];

      const results = await Promise.allSettled(promises);
      
      // Two should succeed, one should fail
      const successful = results.filter(r => r.status === "fulfilled");
      const failed = results.filter(r => r.status === "rejected");
      
      expect(successful).toHaveLength(2);
      expect(failed).toHaveLength(1);

      // Verify final balance
      const balance = await billingService.getUserBalance(testUserId);
      expect(parseFloat(balance?.availableCredits || "0")).toBe(3.0); // 10 - 3 - 4 = 3
      expect(parseFloat(balance?.reservedCredits || "0")).toBe(7.0); // 3 + 4 = 7
    });
  });

  describe("edge cases", () => {
    it("should handle zero amount operations", async () => {
      await billingService.addCredits(testUserId, 10.0, "Setup");
      
      // Zero reservation should be rejected by database constraints
      await expect(
        billingService.reserveCredits(testUserId, "text-extract", 0)
      ).rejects.toThrow();
    });

    it("should handle very small amounts correctly", async () => {
      await billingService.addCredits(testUserId, 0.01, "Minimal amount");
      
      const reservation = await billingService.reserveCredits(testUserId, "text-extract", 0.01);
      const settlement = await billingService.settleReservation(reservation.id, 0.01);
      
      expect(settlement.actualCost).toBe("0.01");
      expect(settlement.refundAmount).toBe("0.00");
    });
  });
});
