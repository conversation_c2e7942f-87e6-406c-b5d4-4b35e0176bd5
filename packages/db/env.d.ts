namespace NodeJS {
  interface ProcessEnv {
    POSTGRES_DIRECT_URL: string;
    JWT_SECRET: string;
    UPSTASH_REDIS_REST_URL: string;
    UPSTASH_REDIS_REST_TOKEN: string;

    // Billing System Configuration
    BILLING_DEFAULT_CREDIT_AMOUNT?: string;
    BILLING_RESERVATION_TIMEOUT_MINUTES?: string;
    BILLING_TEXT_EXTRACT_BASE_COST?: string;
    BILLING_TOKEN_RATE_PER_1K?: string;

    // Cost Calculator Configuration
    COST_CALC_TEXT_EXTRACT_BASE_COST?: string;
    COST_CALC_TEXT_EXTRACT_INPUT_TOKEN_RATE?: string;
    COST_CALC_TEXT_EXTRACT_OUTPUT_TOKEN_RATE?: string;
    COST_CALC_TEXT_EXTRACT_IMAGE_SIZE_MULTIPLIER?: string;
    COST_CALC_DESC_CLEAN_INPUT_TOKEN_RATE?: string;
    COST_CALC_DESC_CLEAN_OUTPUT_TOKEN_RATE?: string;

    // Cron Job Configuration
    BILLING_CLEANUP_INTERVAL_MINUTES?: string;
    BILLING_ENABLE_AUTO_CLEANUP?: string;

    // Test Database
    TEST_DATABASE_URL?: string;
  }
}
