{"id": "9b7d6031-cc24-4260-a1e4-22295437ad5b", "prevId": "9915ac93-7fd6-419e-908b-ad597d311121", "version": "7", "dialect": "postgresql", "tables": {"public.credit_reservations": {"name": "credit_reservations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "text", "primaryKey": false, "notNull": true}, "reserved_amount": {"name": "reserved_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'active'"}, "request_metadata": {"name": "request_metadata", "type": "json", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"credit_reservations_user_id_idx": {"name": "credit_reservations_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "credit_reservations_status_expires_idx": {"name": "credit_reservations_status_expires_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "credit_reservations_user_status_idx": {"name": "credit_reservations_user_status_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"credit_reservations_user_id_users_id_fk": {"name": "credit_reservations_user_id_users_id_fk", "tableFrom": "credit_reservations", "columnsFrom": ["user_id"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users can view own reservations": {"name": "users can view own reservations", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.uid()) = \"credit_reservations\".\"user_id\""}, "service role can manage all reservations": {"name": "service role can manage all reservations", "as": "PERMISSIVE", "for": "ALL", "to": ["service_role"], "using": "true"}}, "checkConstraints": {"status_valid": {"name": "status_valid", "value": "\"credit_reservations\".\"status\" IN ('active', 'settled', 'expired', 'cancelled')"}, "reserved_amount_positive": {"name": "reserved_amount_positive", "value": "\"credit_reservations\".\"reserved_amount\" > 0"}, "expires_at_future": {"name": "expires_at_future", "value": "\"credit_reservations\".\"expires_at\" > \"credit_reservations\".\"created_at\""}}, "isRLSEnabled": false}, "public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "reservation_id": {"name": "reservation_id", "type": "uuid", "primaryKey": false, "notNull": false}, "transaction_type": {"name": "transaction_type", "type": "text", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "service_type": {"name": "service_type", "type": "text", "primaryKey": false, "notNull": false}, "service_metadata": {"name": "service_metadata", "type": "json", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"credit_transactions_user_id_idx": {"name": "credit_transactions_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "credit_transactions_user_created_idx": {"name": "credit_transactions_user_created_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}, "credit_transactions_reservation_idx": {"name": "credit_transactions_reservation_idx", "columns": [{"expression": "reservation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"credit_transactions_user_id_users_id_fk": {"name": "credit_transactions_user_id_users_id_fk", "tableFrom": "credit_transactions", "columnsFrom": ["user_id"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "credit_transactions_reservation_id_credit_reservations_id_fk": {"name": "credit_transactions_reservation_id_credit_reservations_id_fk", "tableFrom": "credit_transactions", "columnsFrom": ["reservation_id"], "tableTo": "credit_reservations", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users can view own transactions": {"name": "users can view own transactions", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.uid()) = \"credit_transactions\".\"user_id\""}, "service role can manage all transactions": {"name": "service role can manage all transactions", "as": "PERMISSIVE", "for": "ALL", "to": ["service_role"], "using": "true"}}, "checkConstraints": {"transaction_type_valid": {"name": "transaction_type_valid", "value": "\"credit_transactions\".\"transaction_type\" IN ('charge', 'refund', 'topup')"}, "amount_not_zero": {"name": "amount_not_zero", "value": "\"credit_transactions\".\"amount\" != 0"}}, "isRLSEnabled": false}, "public.user_credits": {"name": "user_credits", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "available_credits": {"name": "available_credits", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0.00'"}, "reserved_credits": {"name": "reserved_credits", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0.00'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"user_credits_user_id_idx": {"name": "user_credits_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "with": {}, "method": "btree", "concurrently": false}}, "foreignKeys": {"user_credits_user_id_users_id_fk": {"name": "user_credits_user_id_users_id_fk", "tableFrom": "user_credits", "columnsFrom": ["user_id"], "tableTo": "users", "schemaTo": "auth", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {"users can view own credits": {"name": "users can view own credits", "as": "PERMISSIVE", "for": "SELECT", "to": ["authenticated"], "using": "(select auth.uid()) = \"user_credits\".\"user_id\""}, "service role can manage all credits": {"name": "service role can manage all credits", "as": "PERMISSIVE", "for": "ALL", "to": ["service_role"], "using": "true"}}, "checkConstraints": {"available_credits_non_negative": {"name": "available_credits_non_negative", "value": "\"user_credits\".\"available_credits\" >= 0"}, "reserved_credits_non_negative": {"name": "reserved_credits_non_negative", "value": "\"user_credits\".\"reserved_credits\" >= 0"}}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "views": {}, "sequences": {}, "roles": {}, "policies": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}