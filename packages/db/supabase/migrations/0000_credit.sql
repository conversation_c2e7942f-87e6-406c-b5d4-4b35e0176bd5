CREATE TABLE "credit_reservations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"service_type" text NOT NULL,
	"reserved_amount" numeric(10, 2) NOT NULL,
	"status" text DEFAULT 'active' NOT NULL,
	"request_metadata" json,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "status_valid" CHECK ("credit_reservations"."status" IN ('active', 'settled', 'expired', 'cancelled')),
	CONSTRAINT "reserved_amount_positive" CHECK ("credit_reservations"."reserved_amount" > 0),
	CONSTRAINT "expires_at_future" CHECK ("credit_reservations"."expires_at" > "credit_reservations"."created_at")
);
--> statement-breakpoint
ALTER TABLE "credit_reservations" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "credit_transactions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"reservation_id" uuid,
	"transaction_type" text NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"service_type" text,
	"service_metadata" json,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "transaction_type_valid" CHECK ("credit_transactions"."transaction_type" IN ('charge', 'refund', 'topup')),
	CONSTRAINT "amount_not_zero" CHECK ("credit_transactions"."amount" != 0)
);
--> statement-breakpoint
ALTER TABLE "credit_transactions" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
CREATE TABLE "user_credits" (
	"user_id" uuid PRIMARY KEY NOT NULL,
	"available_credits" numeric(10, 2) DEFAULT '0.00' NOT NULL,
	"reserved_credits" numeric(10, 2) DEFAULT '0.00' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "available_credits_non_negative" CHECK ("user_credits"."available_credits" >= 0),
	CONSTRAINT "reserved_credits_non_negative" CHECK ("user_credits"."reserved_credits" >= 0)
);
--> statement-breakpoint
ALTER TABLE "user_credits" ENABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "credit_reservations" ADD CONSTRAINT "credit_reservations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD CONSTRAINT "credit_transactions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "credit_transactions" ADD CONSTRAINT "credit_transactions_reservation_id_credit_reservations_id_fk" FOREIGN KEY ("reservation_id") REFERENCES "public"."credit_reservations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_credits" ADD CONSTRAINT "user_credits_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "credit_reservations_user_id_idx" ON "credit_reservations" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "credit_reservations_status_expires_idx" ON "credit_reservations" USING btree ("status","expires_at");--> statement-breakpoint
CREATE INDEX "credit_reservations_user_status_idx" ON "credit_reservations" USING btree ("user_id","status");--> statement-breakpoint
CREATE INDEX "credit_transactions_user_id_idx" ON "credit_transactions" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "credit_transactions_user_created_idx" ON "credit_transactions" USING btree ("user_id","created_at");--> statement-breakpoint
CREATE INDEX "credit_transactions_reservation_idx" ON "credit_transactions" USING btree ("reservation_id");--> statement-breakpoint
CREATE INDEX "user_credits_user_id_idx" ON "user_credits" USING btree ("user_id");--> statement-breakpoint
CREATE POLICY "users can view own reservations" ON "credit_reservations" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.uid()) = "credit_reservations"."user_id");--> statement-breakpoint
CREATE POLICY "service role can manage all reservations" ON "credit_reservations" AS PERMISSIVE FOR ALL TO "service_role" USING (true);--> statement-breakpoint
CREATE POLICY "users can view own transactions" ON "credit_transactions" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.uid()) = "credit_transactions"."user_id");--> statement-breakpoint
CREATE POLICY "service role can manage all transactions" ON "credit_transactions" AS PERMISSIVE FOR ALL TO "service_role" USING (true);--> statement-breakpoint
CREATE POLICY "users can view own credits" ON "user_credits" AS PERMISSIVE FOR SELECT TO "authenticated" USING ((select auth.uid()) = "user_credits"."user_id");--> statement-breakpoint
CREATE POLICY "service role can manage all credits" ON "user_credits" AS PERMISSIVE FOR ALL TO "service_role" USING (true);