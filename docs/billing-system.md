# Billing System Documentation

## Overview

The billing system implements a **pre-authorization + settlement** model for AI service usage tracking and credit management. This ensures users never exceed their available credits while providing accurate billing based on actual usage.

## Architecture

### Core Components

1. **BillingService** - Handles credit reservations, settlements, and user balance management
2. **CostCalculator** - Converts AI service usage metrics to unified credit costs
3. **Database Schema** - Three core tables with RLS (Row Level Security)
4. **Cron Jobs** - Automated cleanup of expired reservations

### Database Schema

#### `user_credits`
- Tracks available and reserved credits for each user
- Enforces non-negative balance constraints
- RLS: Users can view own credits, service role can manage all

#### `credit_reservations`
- Pre-authorization records with expiration
- Status: active, settled, expired, cancelled
- RLS: Users can view own reservations, service role can manage all

#### `credit_transactions`
- Final billing records for all credit movements
- Types: charge, refund, topup
- RLS: Users can view own transactions, service role can manage all

## Billing Flow

### 1. Pre-Authorization (Reserve Credits)

```typescript
const reservation = await billingService.reserveCredits(
  userId,
  "text-extract",
  estimatedCost,
  { imageSize: image.size, imageType: image.type }
);
```

**Process:**
1. Lock user's credit record
2. Check available balance
3. Create reservation record
4. Move credits from available to reserved
5. Set expiration time (default: 10 minutes)

### 2. Service Execution

```typescript
const result = await geminiService.extractText(image);
```

**Process:**
1. Call AI service (Gemini API)
2. Receive usage metadata (token counts, etc.)
3. Calculate actual cost based on usage

### 3. Settlement (Final Billing)

```typescript
const settlement = await billingService.settleReservation(
  reservationId,
  actualCost,
  result.usageMetadata
);
```

**Process:**
1. Calculate refund amount (reserved - actual)
2. Update user balance (return excess to available)
3. Mark reservation as settled
4. Create transaction records (charge + refund if applicable)

### 4. Error Handling

```typescript
if (reservationId) {
  await billingService.releaseReservation(reservationId, "cancelled");
}
```

**Process:**
1. Return all reserved credits to available
2. Mark reservation as cancelled
3. Create refund transaction record

## Cost Calculation

### Text Extraction

**Components:**
- Base image cost: 0.5 credits
- Image size multiplier: 0.000001 credits per byte
- Input tokens: 0.001 credits per 1000 tokens
- Output tokens: 0.002 credits per 1000 tokens
- Cached tokens: 50% discount

**Example:**
```typescript
const estimatedCost = costCalculator.estimateTextExtractCost(imageSize);
const actualCost = costCalculator.calculateTextExtractCost(usageMetadata, imageSize);
```

### Description Cleaning

**Components:**
- Input tokens: 0.0005 credits per 1000 tokens
- Output tokens: 0.001 credits per 1000 tokens

## API Integration

### Text Extract API

```typescript
// 1. Reserve credits
const reservation = await billingService.reserveCredits(userId, "text-extract", estimatedCost);

try {
  // 2. Call AI service
  const result = await geminiService.extractText(image);
  
  // 3. Calculate actual cost
  const actualCost = costCalculator.calculateTextExtractCost(result.usageMetadata, image.size);
  
  // 4. Settle reservation
  const settlement = await billingService.settleReservation(reservationId, actualCost, result.usageMetadata);
  
  return { ...result, billing: { cost: settlement.actualCost, refunded: settlement.refundAmount } };
} catch (error) {
  // 5. Release reservation on error
  await billingService.releaseReservation(reservationId, "cancelled");
  throw error;
}
```

## Error Handling

### Insufficient Credits

```typescript
try {
  await billingService.reserveCredits(userId, "text-extract", cost);
} catch (error) {
  if (error instanceof InsufficientCreditsError) {
    return c.json({ error: "Insufficient credits", code: "INSUFFICIENT_CREDITS" }, 402);
  }
}
```

### Common Error Codes

- `INSUFFICIENT_CREDITS` - User doesn't have enough available credits
- `RESERVATION_NOT_FOUND` - Invalid reservation ID
- `RESERVATION_ALREADY_SETTLED` - Attempting to settle already processed reservation

## Automated Cleanup

### Cron Job Setup

The system uses Supabase's pg_cron extension to automatically clean up expired reservations:

```sql
-- Runs every 5 minutes
SELECT cron.schedule(
  'cleanup-expired-reservations',
  '*/5 * * * *',
  'SELECT cleanup_expired_reservations();'
);
```

### Cleanup Process

1. Find reservations with `status = 'active'` and `expires_at < NOW()`
2. Return reserved credits to available balance
3. Mark reservations as expired
4. Create refund transaction records

## Monitoring

### Billing Statistics

```sql
SELECT * FROM get_billing_stats();
```

Returns:
- Total users with credits
- Total credits issued/spent
- Transaction counts
- Active/expired reservations

### Cron Job Status

```sql
SELECT * FROM billing_cron_status;
```

Returns:
- Job schedule and status
- Last/next run times
- Health indicators

### User Balance Overview

```sql
SELECT * FROM billing_overview WHERE user_id = 'user-id';
```

Returns:
- Current balance breakdown
- Recent activity summary
- Reservation counts

## Administration

### Add Credits to User

```typescript
await billingService.addCredits(userId, 50.0, "Admin credit top-up");
```

### Initialize New User

```sql
SELECT initialize_user_credits('user-id', 10.00);
```

### Manual Cleanup

```sql
SELECT cleanup_expired_reservations();
```

## Configuration

### Environment Variables

```env
# Billing Configuration
BILLING_DEFAULT_CREDIT_AMOUNT=10.00
BILLING_RESERVATION_TIMEOUT_MINUTES=10

# Cost Calculator Rates
COST_CALC_TEXT_EXTRACT_BASE_COST=0.5
COST_CALC_TEXT_EXTRACT_INPUT_TOKEN_RATE=0.001
COST_CALC_TEXT_EXTRACT_OUTPUT_TOKEN_RATE=0.002
```

### Rate Updates

```typescript
costCalculator.updateServiceRates({
  textExtract: {
    baseImageCost: 1.0,
    inputTokenRate: 0.002,
  }
});
```

## Security

### Row Level Security (RLS)

- **Users**: Can only view their own credits, reservations, and transactions
- **Service Role**: Can manage all billing data for API operations
- **Anonymous**: No access to billing data

### Database Constraints

- Non-negative credit balances
- Positive reservation amounts
- Valid status transitions
- Expiration time validation

## Performance Considerations

### Database Optimization

- Indexes on user_id, status, and created_at columns
- FOR UPDATE locks prevent race conditions
- Transaction isolation ensures consistency

### Concurrent Operations

The system handles concurrent reservations safely using database-level locking:

```typescript
// Locks user's credit row during reservation
const userCredit = await tx
  .select()
  .from(userCredits)
  .where(eq(userCredits.userId, userId))
  .for("update");
```

## Testing

### Unit Tests

```bash
cd packages/db
npm test
```

Tests cover:
- Credit reservation/settlement flows
- Concurrent operation safety
- Error handling scenarios
- Cost calculation accuracy

### Integration Tests

Test the complete billing flow with actual database operations and API calls.

## Troubleshooting

### Common Issues

1. **Reservations not expiring**: Check cron job status
2. **Balance inconsistencies**: Verify transaction logs
3. **High costs**: Review rate configuration
4. **Performance issues**: Check database indexes

### Debug Queries

```sql
-- Check user's billing history
SELECT * FROM credit_transactions WHERE user_id = 'user-id' ORDER BY created_at DESC;

-- Find stuck reservations
SELECT * FROM credit_reservations WHERE status = 'active' AND expires_at < NOW();

-- Monitor cron job performance
SELECT * FROM cron.job_run_details WHERE jobname = 'cleanup-expired-reservations' ORDER BY start_time DESC LIMIT 10;
```
